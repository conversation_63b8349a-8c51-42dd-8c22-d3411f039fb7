<?php

namespace App\Models;

use App\Models\Concerns\Checklist\HandlesChecklistAttributes;
use App\Models\Concerns\Checklist\HandlesChecklistRelationships;
use Illuminate\Database\Eloquent\Model;

/**
 * Checklist model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  string $name
 * @property  string $type
 * @property  string $additional_info
 * @property  bool $active
 * @property  bool $sends_email_on_service_order_conclusion
 * @property  string $conclusion_email_addresses
 * @property  bool $use_report_template
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  string $friendly_type
 *
 * @property  \Illuminate\Support\Collection|\App\Models\ChecklistStep[] $checklistSteps
 * @property  \Illuminate\Support\Collection|\App\Models\ChecklistReportTemplate[] $checklistReportTemplates
 */
class Checklist extends Model
{
    use HandlesChecklistAttributes;
    use HandlesChecklistRelationships;

    protected $fillable = [
        'name',
        'type',
        'additional_info',
        'active',
        'sends_email_on_service_order_conclusion',
        'conclusion_email_addresses',
        'use_report_template',
    ];

    protected $casts = [
        'active' => 'bool',
        'sends_email_on_service_order_conclusion' => 'bool',
        'use_report_template' => 'bool',
    ];
}
