<?php

namespace App\Models;

use App\Models\Concerns\TenantSettings\HandlesTenantSettingsRelationships;
use Illuminate\Database\Eloquent\Model;

/**
 * Tenant settings model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $service_order_default_smtp_configuration_id
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \App\Models\SmtpConfiguration $serviceOrderDefaultSmtpConfiguration
 */
class TenantSettings extends Model
{
    use HandlesTenantSettingsRelationships;

    protected $fillable = [
        'service_order_default_smtp_configuration_id',
    ];
}
