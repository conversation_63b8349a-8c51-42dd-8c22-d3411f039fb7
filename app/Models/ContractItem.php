<?php

namespace App\Models;

use App\Models\Concerns\ContractItem\HandlesContractItemAttributes;
use App\Models\Concerns\ContractItem\HandlesContractItemRelationships;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Contract item model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $contract_id
 * @property  int $equipment_id
 * @property  int $service_type_id
 * @property  int $checklist_id
 * @property  int $quantity
 * @property  int $interval
 * @property  string $period
 * @property  string $additional_info
 * @property  \Carbon\Carbon $started_at
 * @property  \Carbon\Carbon $ended_at
 * @property  bool $generate_service_order_for_starting_date
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  string $friendly_period
 *
 * @property  \App\Models\Contract $contract
 * @property  \App\Models\Equipment $equipment
 * @property  \App\Models\ServiceType $serviceType
 * @property  \App\Models\Checklist $checklist
 */
class ContractItem extends Model
{
    use HandlesContractItemAttributes;
    use HandlesContractItemRelationships;
    use HasFactory;

    protected $fillable = [
        'contract_id',
        'equipment_id',
        'service_type_id',
        'checklist_id',
        'quantity',
        'interval',
        'period',
        'additional_info',
        'started_at',
        'ended_at',
        'generate_service_order_for_starting_date',
    ];

    protected $casts = [
        'contract_id' => 'int',
        'equipment_id' => 'int',
        'service_type_id' => 'int',
        'checklist_id' => 'int',
        'quantity' => 'int',
        'interval' => 'int',
        'generate_service_order_for_starting_date' => 'bool',
    ];
}
