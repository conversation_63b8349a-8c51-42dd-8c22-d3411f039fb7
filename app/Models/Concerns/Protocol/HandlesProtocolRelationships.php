<?php

namespace App\Models\Concerns\Protocol;

use App\Models\Customer;
use App\Models\ProtocolEquipment;
use App\Models\ProtocolServiceOrder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

trait HandlesProtocolRelationships
{
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function protocolEquipment(): HasMany
    {
        return $this->hasMany(ProtocolEquipment::class);
    }

    public function protocolServiceOrders(): HasMany
    {
        return $this->hasMany(ProtocolServiceOrder::class);
    }
}
