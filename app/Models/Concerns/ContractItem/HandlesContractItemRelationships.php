<?php

namespace App\Models\Concerns\ContractItem;

use App\Models\Checklist;
use App\Models\Contract;
use App\Models\Equipment;
use App\Models\ServiceType;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

trait HandlesContractItemRelationships
{
    public function contract(): BelongsTo
    {
        return $this->belongsTo(Contract::class);
    }

    public function equipment(): BelongsTo
    {
        return $this->belongsTo(Equipment::class);
    }

    public function serviceType(): BelongsTo
    {
        return $this->belongsTo(ServiceType::class);
    }

    public function checklist(): BelongsTo
    {
        return $this->belongsTo(Checklist::class);
    }
}
