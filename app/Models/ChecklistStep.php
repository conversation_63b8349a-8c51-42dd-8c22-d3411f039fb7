<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Checklist step model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $checklist_id
 * @property  int $sequence
 * @property  string $name
 * @property  string $data_type
 * @property  string $instructions
 * @property  bool $requires_comment
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \App\Models\Checklist $checklist
 *
 * @property  \Illuminate\Support\Collection|\App\Models\ChecklistStepOption[] $checklistStepOption
 */
class ChecklistStep extends Model
{
    protected $fillable = [
        'checklist_id',
        'sequence',
        'name',
        'data_type',
        'instructions',
        'requires_comment',
    ];

    protected $casts = [
        'checklist_id' => 'int',
        'requires_comment' => 'bool',
    ];

    public function checklist(): BelongsTo
    {
        return $this->belongsTo(Checklist::class);
    }

    public function checklistStepOptions(): Has<PERSON>any
    {
        return $this->hasMany(ChecklistStepOption::class);
    }
}
