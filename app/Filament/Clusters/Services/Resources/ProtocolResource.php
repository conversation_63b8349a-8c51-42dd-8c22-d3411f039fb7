<?php

namespace App\Filament\Clusters\Services\Resources;

use App\Actions\Checklist\Queries\GetActiveChecklistsByName;
use App\Actions\Customer\Queries\GetCustomersByNameTradingNameOrTaxIdentificationNumber;
use App\Actions\Equipment\CreateEquipment;
use App\Actions\Equipment\Integrations\ErpFlex\GetEquipmentFromErpFlex;
use App\Actions\Equipment\Queries\GetEquipmentBySerialNumberOrName;
use App\Actions\IntegrationSetting\Queries\GetActiveIntegrationSettingsByIntegrationTypeId;
use App\Actions\Protocol\GenerateProtocolServiceOrders;
use App\Actions\Protocol\Integrations\GetInvoiceDetailsFromIntegrations;
use App\Actions\ServiceType\Queries\GetServiceTypes;
use App\Actions\ThirdPartyEquipment\Queries\GetThirdPartyEquipmentByThirdPartyId;
use App\Core\Filament\Filters\TableTextFilter;
use App\Filament\Clusters\Services;
use App\Filament\Clusters\Services\Resources\ProtocolResource\Pages;
use App\Models\Equipment;
use App\Models\IntegrationType;
use App\Models\Protocol;
use App\Models\ProtocolEquipment;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Tabs\Tab;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Resources\Resource;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Throwable;

class ProtocolResource extends Resource
{
    protected static ?string $model = Protocol::class;
    protected static ?string $modelLabel = 'protocolo';
    protected static ?string $navigationIcon = 'heroicon-o-document';
    protected static ?string $cluster = Services::class;
    protected static ?int $navigationSort = 2;

    public static function getEloquentQuery(): Builder
    {
        return Protocol::query()
            ->with('protocolServiceOrders');
    }

    public static function form(Form $form): Form
    {
        return $form->schema([
            Grid::make(1)->schema([
                Tabs::make()->schema([
                    Tab::make('general')
                        ->label('Geral')
                        ->schema([
                            Grid::make(4)->schema([
                                TextInput::make('code')
                                    ->label(__('protocols.forms.fields.code')),
                                TextInput::make('invoice_number')
                                    ->label(__('protocols.forms.fields.invoice_number'))
                                    ->lazy()
                                    ->afterStateUpdated(fn(?string $state, Set $set) => self::handleInvoiceNumberIntegrations($state, $set))
                                    ->suffix(function (?string $state, Get $get): ?string {
                                        if (is_null($state)) {
                                            return null;
                                        }

                                        return count($get('protocol_equipment')) > 0
                                            ? null
                                            : 'Buscando';
                                    }),
                                Select::make('customer_id')
                                    ->label(__('protocols.forms.fields.customer_id'))
                                    ->relationship('customer', 'name')
                                    ->placeholder('Digite a razão social, fantasia ou o CNPJ')
                                    ->columnSpan(2)
                                    ->reactive()
                                    ->searchable()
                                    ->getSearchResultsUsing(fn(string $search): array => GetCustomersByNameTradingNameOrTaxIdentificationNumber::run($search, true)->pluck('name', 'id')->toArray()),
                            ]),
                            Grid::make(1)->schema([
                                Textarea::make('description')
                                    ->label(__('protocols.forms.fields.description'))
                                    ->rows(3),
                            ]),
                        ]),
                    Tab::make('equipment')
                        ->label('Equipamentos')
                        ->schema([
                            TableRepeater::make('protocol_equipment')
                                ->hiddenLabel()
                                ->relationship('protocolEquipment')
                                ->defaultItems(0)
                                ->addActionLabel('Adicionar equipamento')
                                ->headers([
                                    Header::make(__('protocol_equipment.forms.fields.equipment_id'))
                                        ->width('50%')
                                        ->markAsRequired(),
                                    Header::make(__('equipment.forms.fields.serial_number'))
                                        ->width('20%'),
                                    Header::make(__('equipment.forms.fields.equipment_type_id')),
                                ])
                                ->schema([
                                    Select::make('equipment_id')
                                        ->relationship('equipment', 'name')
                                        ->reactive()
                                        ->searchable()
                                        ->getSearchResultsUsing(fn(string $search): array => GetEquipmentBySerialNumberOrName::run($search, true)->pluck('name', 'id')->toArray())
                                        ->afterStateUpdated(function (?string $state, Set $set): void {
                                            if (!$state) {
                                                $set('serial_number', null);
                                                $set('equipment_type_id', null);
                                                return;
                                            }

                                            $equipment = Equipment::find($state);

                                            $set('serial_number', $equipment->serial_number);
                                            $set('equipment_type_id', $equipment->equipmentType->name);
                                        })
                                        ->afterStateHydrated(function (?string $state, Set $set): void {
                                            if (!$state) {
                                                $set('serial_number', null);
                                                $set('equipment_type_id', null);
                                                return;
                                            }

                                            $equipment = Equipment::find($state);

                                            $set('serial_number', $equipment->serial_number);
                                            $set('equipment_type_id', $equipment->equipmentType->name);
                                        })
                                        ->createOptionForm([
                                            Grid::make(2)->schema([
                                                TextInput::make('serial_number')
                                                    ->label(__('equipment.forms.fields.serial_number'))
                                                    ->required(),
                                                Select::make('equipment_type_id')
                                                    ->label(__('equipment.forms.fields.equipment_type_id'))
                                                    ->relationship('equipmentType', 'name')
                                                    ->required(),
                                            ]),
                                            Grid::make(1)->schema([
                                                TextInput::make('name')
                                                    ->label(__('equipment.forms.fields.name'))
                                                    ->required(),
                                            ]),
                                        ])
                                        ->createOptionUsing(function (Get $get, array $data): void {
                                            try {
                                                CreateEquipment::run(array_merge($data, ['customer_id' => $get('../../customer_id')]));
                                                success_notification(__('equipment.responses.create.success'))->send();
                                            } catch (Throwable $th) {
                                                error_notification($th->getMessage());
                                            }
                                        }),
                                    TextInput::make('serial_number')
                                        ->readOnly(),
                                    TextInput::make('equipment_type_id')
                                        ->readOnly()
                                        ->formatStateUsing(fn(?ProtocolEquipment $protocolEquipment): string => $protocolEquipment?->equipment?->equipmentType?->name ?? ''),
                                ])
                        ]),
                ]),
            ]),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('code')
                    ->label(__('protocols.forms.fields.code')),
                TextColumn::make('customer.name')
                    ->label(__('protocols.forms.fields.customer_id')),
                TextColumn::make('protocol_equipment_count')
                    ->label('Quantidade de equipamentos')
                    ->counts('protocolEquipment'),
                TextColumn::make('created_at')
                    ->label(__('protocols.forms.fields.created_at'))
                    ->formatStateUsing(fn(Protocol $protocol): string => format_datetime($protocol->created_at)),
            ])
            ->filters([
                TableTextFilter::buildLike('protocols', 'code'),
                TableTextFilter::buildRelation('protocols', 'customer_id', 'customer', 'name'),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\ViewAction::make()
                        ->modalWidth(MaxWidth::SevenExtraLarge),
                    Tables\Actions\EditAction::make()
                        ->modalWidth(MaxWidth::SevenExtraLarge)
                        ->successNotification(success_notification(__('protocols.responses.update.success')))
                        ->visible(fn(Protocol $protocol): bool => $protocol->protocolServiceOrders->count() === 0),
                    Tables\Actions\Action::make('generate_service_orders')
                        ->label('Gerar ordens de serviço')
                        ->icon('heroicon-o-document')
                        ->visible(fn(Protocol $protocol): bool => $protocol->protocolServiceOrders->count() === 0)
                        ->form([
                            Grid::make(1)->schema([
                                Select::make('service_type_id')
                                    ->label(__('service_orders.forms.fields.service_type_id'))
                                    ->required()
                                    ->options(GetServiceTypes::run()->pluck('name', 'id')->toArray()),
                            ]),
                            Grid::make(1)->schema([
                                TableRepeater::make('checklist_ids')
                                    ->label('Roteiros')
                                    ->defaultItems(1)
                                    ->addActionLabel('Vincular roteiro')
                                    ->headers([
                                        Header::make('Roteiro'),
                                    ])
                                    ->schema([
                                        Select::make('checklist_id')
                                            ->searchable()
                                            ->lazy()
                                            ->getSearchResultsUsing(fn(string $search): array => GetActiveChecklistsByName::run($search)->pluck('name', 'id')->toArray()),
                                    ]),
                            ]),
                        ])
                        ->action(function (Protocol $protocol, array $data): void {
                            $checklistIds = array_values(
                                array_map(fn(array $item) => (int)$item['checklist_id'], $data['checklist_ids'])
                            );

                            try {
                                GenerateProtocolServiceOrders::run($protocol, $data['service_type_id'], $checklistIds);
                                success_notification(__('protocols.responses.generate_service_orders.success'))->send();
                            } catch (Throwable $th) {
                                error_notification($th->getMessage())->send();
                            }
                        })
                        ->requiresConfirmation(),
                    Tables\Actions\DeleteAction::make()
                        ->visible(fn(Protocol $protocol): bool => $protocol->protocolServiceOrders->count() === 0)
                        ->successNotification(success_notification(__('protocols.responses.delete.success'))),
                ]),
            ])
            ->bulkActions([])
            ->emptyStateHeading('Ainda sem protocolos')
            ->emptyStateDescription('Assim que você lançar seus protocolos, eles aparecerão nesta listagem.');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageProtocols::route('/'),
        ];
    }

    public static function handleInvoiceNumberIntegrations(?string $invoiceNumber, Set $set): void
    {
        if (is_null($invoiceNumber)) {
            return;
        }

        $invoiceDetails = GetInvoiceDetailsFromIntegrations::run($invoiceNumber);

        if (count($invoiceDetails) === 0) {
            return;
        }

        $set('customer_id', $invoiceDetails[0]['customer_id']);

        $protocolEquipment = array_map(function (array $apiEquipment): array {
            /** @var \App\Models\ThirdPartyEquipment|null $thirdPartyEquipment */
            $thirdPartyEquipment = GetThirdPartyEquipmentByThirdPartyId::run(IntegrationType::TYPE_ERP_FLEX, $apiEquipment['protocol_equipment']->SD1_IDSB1);

            if ($thirdPartyEquipment && $thirdPartyEquipment->equipment_id) {
                return [
                    'equipment_id' => $thirdPartyEquipment->equipment_id,
                    'serial_number' => $thirdPartyEquipment->equipment->serial_number,
                    'equipment_type_id' => $thirdPartyEquipment->equipment->equipmentType->name,
                ];
            }

            GetEquipmentFromErpFlex::run(
                GetActiveIntegrationSettingsByIntegrationTypeId::run(IntegrationType::TYPE_ERP_FLEX),
                false,
                null,
                $apiEquipment['protocol_equipment']->SD1_IDSB1,
            );

            /** @var \App\Models\ThirdPartyEquipment|null $thirdPartyEquipment */
            $thirdPartyEquipment = GetThirdPartyEquipmentByThirdPartyId::run(IntegrationType::TYPE_ERP_FLEX, $apiEquipment['protocol_equipment']->SD1_IDSB1);

            return [
                'equipment_id' => $thirdPartyEquipment->equipment_id,
                'serial_number' => $thirdPartyEquipment->equipment->serial_number,
                'equipment_type_id' => $thirdPartyEquipment->equipment->equipmentType->name,
            ];
        }, $invoiceDetails);

        $set('protocol_equipment', $protocolEquipment);
    }
}
