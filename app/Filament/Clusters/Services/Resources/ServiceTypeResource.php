<?php

namespace App\Filament\Clusters\Services\Resources;

use App\Core\Filament\Filters\TableTextFilter;
use App\Filament\Clusters\Services;
use App\Filament\Clusters\Services\Resources\ServiceTypeResource\Pages;
use App\Models\ServiceType;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class ServiceTypeResource extends Resource
{
    protected static ?string $model = ServiceType::class;
    protected static ?string $modelLabel = 'tipo de serviço';
    protected static ?string $pluralModelLabel = 'tipos de serviço';
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $cluster = Services::class;
    protected static ?int $navigationSort = 6;

    public static function form(Form $form): Form
    {
        return $form->schema([
            Grid::make(4)->schema([
                TextInput::make('name')
                    ->label(__('service_types.forms.fields.name'))
                    ->required()
                    ->columnSpan(2),
                TextInput::make('estimated_duration_in_minutes')
                    ->label(__('service_types.forms.fields.estimated_duration_in_minutes'))
                    ->required()
                    ->numeric(),
                TextInput::make('default_amount')
                    ->label(__('service_types.forms.fields.default_amount'))
                    ->required()
                    ->formatStateUsing(fn(?ServiceType $serviceType): string => $serviceType?->friendly_default_amount),
            ]),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label(__('service_types.forms.fields.name')),
                TextColumn::make('estimated_duration_in_minutes')
                    ->label(__('service_types.forms.fields.estimated_duration_in_minutes')),
                TextColumn::make('default_amount')
                    ->label(__('service_types.forms.fields.default_amount'))
                    ->formatStateUsing(fn(ServiceType $serviceType): string => $serviceType->friendly_default_amount),
            ])
            ->filters([
                TableTextFilter::buildLike('service_types', 'name'),
            ])
            ->actions([
                ActionGroup::make([
                    ViewAction::make(),
                    EditAction::make()
                        ->successNotification(success_notification(__('service_types.responses.update.success'))),
                    DeleteAction::make()
                        ->successNotification(success_notification(__('service_types.responses.delete.success'))),
                ]),
            ])
            ->bulkActions([])
            ->emptyStateHeading('Ainda sem tipos de serviço')
            ->emptyStateDescription('Assim que você cadastrar seus tipos de serviço, eles aparecerão nesta listagem.');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageServiceTypes::route('/'),
        ];
    }
}
