<?php

namespace App\Filament\Clusters\AccessControl\Resources\UserResource\Pages;

use App\Actions\User\CreateUser;
use App\Filament\Clusters\AccessControl\Resources\UserResource;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;
use Throwable;

class ManageUsers extends ManageRecords
{
    protected static string $resource = UserResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->using(function (array $data): void {
                    try {
                        CreateUser::run($data);
                    } catch (Throwable $th) {
                        error_notification($th->getMessage())->send();
                    }
                })
                ->successNotification(success_notification(__('users.responses.create.success'))),
        ];
    }
}
