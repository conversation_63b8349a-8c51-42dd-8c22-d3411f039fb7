<?php

namespace App\Actions\ServiceOrder;

use App\Enums\ChecklistStepDataTypeEnum;
use App\Models\ServiceOrder;
use App\Models\ServiceOrderChecklist;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;

class CreateServiceOrderExecutionChecklistSteps
{
    use AsAction;

    private int $sequence = 1;
    private ServiceOrder $serviceOrder;

    public function handle(ServiceOrder $serviceOrder, ?ServiceOrderChecklist $serviceOrderChecklist = null): ServiceOrder
    {
        $this->serviceOrder = $serviceOrder;

        $this->sequence = $this->serviceOrder->serviceOrderExecutionChecklistSteps()->count() + 1;

        return DB::transaction(function () use ($serviceOrderChecklist) {
            if ($serviceOrderChecklist) {
                $this->createChecklistSteps($serviceOrderChecklist);
                return $this->serviceOrder;
            }

            $this->serviceOrder->serviceOrderChecklists->each(function (ServiceOrderChecklist $serviceOrderChecklist) {
                $this->createChecklistSteps($serviceOrderChecklist);
            });

            return $this->serviceOrder;
        });
    }

    private function createChecklistSteps(ServiceOrderChecklist $serviceOrderChecklist): void
    {
        $checklistSteps = $serviceOrderChecklist->checklist->checklistSteps;

        for ($i = 0; $i < $checklistSteps->count(); $i++) {
            /** @var \App\Models\ServiceOrderExecutionChecklistStep $serviceOrderExecutionChecklistStep */
            $serviceOrderExecutionChecklistStep = $this->serviceOrder->serviceOrderExecutionChecklistSteps()->create([
                'sequence' => $this->sequence,
                'checklist_id' => $checklistSteps[$i]->checklist_id,
                'checklist_step_name' => $checklistSteps[$i]->name,
                'data_type' => $checklistSteps[$i]->data_type,
                'requires_comment' => $checklistSteps[$i]->requires_comment,
                'is_checklist_final_step' => $checklistSteps->count() === $i + 1
            ]);

            $this->sequence++;

            if (in_array($checklistSteps[$i]->data_type, [ChecklistStepDataTypeEnum::MultipleChoice->value, ChecklistStepDataTypeEnum::SingleChoice->value])) {
                foreach ($checklistSteps[$i]->checklistStepOptions as $checklistStepOption) {
                    $serviceOrderExecutionChecklistStep->serviceOrderExecutionChecklistStepOptions()->create([
                        'sequence' => $checklistStepOption->sequence,
                        'value' => $checklistStepOption->value,
                        'requires_comment' => $checklistStepOption->requires_comment,
                    ]);
                }
            }
        }
    }
}
