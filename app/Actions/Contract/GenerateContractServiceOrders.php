<?php

namespace App\Actions\Contract;

use App\Models\Contract;
use App\Models\ContractItem;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class GenerateContractServiceOrders
{
    use AsAction;

    public function handle(Contract $contract): Contract
    {
        try {
            DB::transaction(function () use ($contract): void {
                $contract->contractItems->each(function (ContractItem $contractItem) {
                    
                });
            });

            return $contract;
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
