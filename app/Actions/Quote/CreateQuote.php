<?php

namespace App\Actions\Quote;

use App\Models\Product;
use App\Models\Quote;
use App\Models\ServiceType;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class CreateQuote
{
    use AsAction;

    public function handle(array $data): Quote
    {
        try {
            return DB::transaction(function () use ($data): Quote {
                $quoteItems = $data['quote_items'];
                unset($data['quote_items']);

                /** @var \App\Models\Quote $quote */
                $quote = Quote::create($data);

                foreach ($quoteItems as $quoteItem) {
                    if ($quoteItem['type'] === Product::class) {
                        $quoteItem['product_id'] = $quoteItem['item_id'];
                    } elseif ($quoteItem['type'] === ServiceType::class) {
                        $quoteItem['service_type_id'] = $quoteItem['item_id'];
                    }

                    $quote->quoteItems()->create($quoteItem);
                }

                return $quote;
            });
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
